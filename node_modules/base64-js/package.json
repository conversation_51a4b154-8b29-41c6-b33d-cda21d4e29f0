{"name": "base64-js", "description": "Base64 encoding/decoding in pure JS", "version": "1.3.1", "author": "<PERSON><PERSON> <PERSON> Little <<EMAIL>>", "bugs": {"url": "https://github.com/beatgammit/base64-js/issues"}, "devDependencies": {"benchmark": "^2.1.4", "browserify": "^16.3.0", "standard": "*", "tape": "4.x", "uglify-js": "^3.6.0"}, "homepage": "https://github.com/beatgammit/base64-js", "keywords": ["base64"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/beatgammit/base64-js.git"}, "scripts": {"build": "browserify -s base64js -r ./ | uglifyjs -m > base64js.min.js", "lint": "standard", "test": "npm run lint && npm run unit", "unit": "tape test/*.js"}}