<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <title>PNG.js example</title>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <script src="../zlib.js"></script>
  <script src="../png.js"></script>  
</head>

<body>
  <h2>PNG.js browser demo</h2>  
  <div id="buttons"></div>
  <canvas id="png-canvas"></canvas>
  
  <script>
      var files = ['ball.png',
        'chompy.png',
        'djay-indexed.png',
        'djay.png',
        'laptop.png',
        'loading.png',
        'spinfox.png',
        'trees.png']
      var buttonsEl = document.getElementById('buttons')
      buttonsEl.innerHTML = files.reduce((result, file) => {
        return result.concat(`<button data-file="${file}">${file}</button>`)
      }, '')
      buttonsEl.addEventListener('click', (e) => {        
        var file = e.target.dataset.file
        if (!file) {
          return
        }
        PNG.load(`../images/${file}`, document.getElementById('png-canvas'))
      })
    </script>
</body>

</html>