{"name": "pdfmake", "version": "0.2.20", "description": "Client/server side PDF printing in pure JavaScript", "main": "src/printer.js", "browser": "build/pdfmake.js", "directories": {"test": "tests"}, "dependencies": {"@foliojs-fork/linebreak": "^1.1.2", "@foliojs-fork/pdfkit": "^0.15.3", "iconv-lite": "^0.6.3", "xmldoc": "^2.0.1"}, "devDependencies": {"@babel/cli": "^7.27.2", "@babel/core": "^7.27.1", "@babel/plugin-transform-modules-commonjs": "^7.27.1", "@babel/preset-env": "^7.27.2", "@eslint/js": "^9.26.0", "assert": "^2.1.0", "babel-loader": "^10.0.0", "brfs": "^2.0.2", "browserify-zlib": "^0.2.0", "buffer": "^6.0.3", "core-js": "3.19.0", "eslint": "^9.26.0", "eslint-plugin-jsdoc": "^50.6.11", "expose-loader": "^5.0.1", "file-saver": "^2.0.5", "globals": "^16.1.0", "mocha": "^11.2.2", "npm-run-all": "^4.1.5", "process": "^0.11.10", "rewire": "^7.0.0", "shx": "^0.4.0", "sinon": "^20.0.0", "source-map-loader": "^5.0.0", "stream-browserify": "^3.0.0", "string-replace-webpack-plugin": "^0.1.3", "svg-to-pdfkit": "^0.1.8", "terser-webpack-plugin": "^5.3.14", "transform-loader": "^0.2.4", "util": "^0.12.5", "webpack": "^5.99.8", "webpack-cli": "^6.0.1"}, "engines": {"node": ">=18"}, "scripts": {"test": "run-s build mocha", "build": "run-s build:3rdparty build:browser", "build:3rdparty": "shx cp node_modules/svg-to-pdfkit/source.js src/3rd-party/svg-to-pdfkit/source.js && shx cp node_modules/svg-to-pdfkit/LICENSE src/3rd-party/svg-to-pdfkit/LICENSE", "build:browser": "webpack", "build:browser-standard-fonts": "webpack --config webpack-standardfonts.config.js", "build:vfs": "node build-vfs.js \"./examples/fonts\"", "build:examples": "node build-examples.js", "lint": "eslint \"./src/**/*.js\" \"./tests/**/*.js\" \"./examples/**/*.js\"", "mocha": "mocha --reporter spec \"./tests/**/*.js\"", "playground": "node dev-playground/server.js"}, "repository": {"type": "git", "url": "git://github.com/bpampuch/pdfmake.git"}, "keywords": ["pdf", "javascript", "printing", "layout"], "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/bpampuch/pdfmake/issues"}, "homepage": "http://pdfmake.org", "config": {"blanket": {"pattern": "src", "data-cover-never": ["node_modules", "tests"]}}}