{"name": "xmldoc", "description": "A lightweight XML Document class for JavaScript.", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://nfarina.com"}, "version": "2.0.2", "packageManager": "yarn@4.7.0", "type": "commonjs", "main": "./lib/xmldoc.js", "types": "./dist/xmldoc.d.ts", "exports": {".": {"types": "./dist/xmldoc.d.ts", "import": "./dist/xmldoc.js", "require": "./lib/xmldoc.js", "default": "./lib/xmldoc.js"}}, "files": ["dist/*.js", "dist/*.d.ts", "dist/*.map", "lib", "LICENSE", "README.md", "CHANGELOG.md", "index.js", "index.d.ts"], "scripts": {"test": "yarn test:js || true && yarn test:ts", "test:js": "tap test/*.js", "test:ts": "vitest run test/ts", "coverage": "npm test:js -- --cov --coverage-report=html", "clean": "rm -rf dist", "build": "tsc --sourceMap", "prepublishOnly": "npm run clean && npm run build", "format": "prettier --write ."}, "license": "MIT", "repository": {"type": "git", "url": "git://github.com/nfarina/xmldoc.git"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "readmeFilename": "README.md", "dependencies": {"sax": "^1.2.4"}, "devDependencies": {"@types/sax": "^1.2.7", "prettier": "^2.7.1", "tap": "^16.3.0", "typescript": "^5.8.2", "vitest": "^3.1.3"}, "engines": {"node": ">=12.0.0"}, "keywords": ["xml", "sax", "parser", "xpath", "document"]}