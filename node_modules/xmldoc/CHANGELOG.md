# Change Log

## [v2.0.0](https://github.com/nfarina/xmldoc/tree/v2.0.0) (2024)

**Major Changes:**

- Complete TypeScript rewrite with full type definitions
- Dual package support for both CommonJS and ESM environments
- Maintained backwards compatibility with existing code
- Updated `sax` dependency to 1.2.4
- Added support for modern module resolution via package.json exports field
- Improved HTML compatibility with self-closing tags
- Enhanced documentation and examples
- Added proper TypeScript declaration files

## [v1.0.0](https://github.com/nfarina/xmldoc/tree/v1.0.0) (2016-12-26)

[Full Changelog](https://github.com/nfarina/xmldoc/compare/v0.5.1...v1.0.0)

**Closed issues:**

- Excellent library with a beautiful, clean API [\#42](https://github.com/nfarina/xmldoc/issues/42)
- Order of elements changed [\#41](https://github.com/nfarina/xmldoc/issues/41)
- While writing back xml document to a file it removes all comments [\#39](https://github.com/nfarina/xmldoc/issues/39)
- react native using xmldoc to parser xml [\#38](https://github.com/nfarina/xmldoc/issues/38)
- Order of val in relation to children? [\#37](https://github.com/nfarina/xmldoc/issues/37)

**Merged pull requests:**

- Fix CData overwriting bug [\#43](https://github.com/nfarina/xmldoc/pull/43) ([calebmer](https://github.com/calebmer))

## [v0.5.1](https://github.com/nfarina/xmldoc/tree/v0.5.1) (2016-05-12)

[Full Changelog](https://github.com/nfarina/xmldoc/compare/v0.5.0...v0.5.1)

**Closed issues:**

- Release notes for 0.5 [\#35](https://github.com/nfarina/xmldoc/issues/35)

**Merged pull requests:**

- GLOBAL is producing deprecation warnings in node V6 [\#36](https://github.com/nfarina/xmldoc/pull/36) ([jmalins](https://github.com/jmalins))

## [v0.5.0](https://github.com/nfarina/xmldoc/tree/v0.5.0) (2016-04-27)

[Full Changelog](https://github.com/nfarina/xmldoc/compare/v0.4.0...v0.5.0)

**Closed issues:**

- Incorrect escaping of &lt; &gt; [\#29](https://github.com/nfarina/xmldoc/issues/29)
- Update tag for v0.4.0 [\#28](https://github.com/nfarina/xmldoc/issues/28)
- Error parsing coments out of XML scope [\#27](https://github.com/nfarina/xmldoc/issues/27)
- Support of xml comments [\#22](https://github.com/nfarina/xmldoc/issues/22)
- Question on usage [\#20](https://github.com/nfarina/xmldoc/issues/20)

**Merged pull requests:**

- Handle "doctype" elements. [\#34](https://github.com/nfarina/xmldoc/pull/34) ([nfarina](https://github.com/nfarina))
- Support XML comments [\#33](https://github.com/nfarina/xmldoc/pull/33) ([nfarina](https://github.com/nfarina))
- Correctly handle \(discard\) tags that come after the root XML document node [\#32](https://github.com/nfarina/xmldoc/pull/32) ([nfarina](https://github.com/nfarina))
- Add tests [\#31](https://github.com/nfarina/xmldoc/pull/31) ([nfarina](https://github.com/nfarina))
- Fixing \#29 - Incorrect escaping of &lt; &gt; [\#30](https://github.com/nfarina/xmldoc/pull/30) ([buholzer](https://github.com/buholzer))

## [v0.4.0](https://github.com/nfarina/xmldoc/tree/v0.4.0) (2015-11-16)

[Full Changelog](https://github.com/nfarina/xmldoc/compare/v0.3.1...v0.4.0)

**Closed issues:**

- Support DOCTYPE or ignore it please [\#24](https://github.com/nfarina/xmldoc/issues/24)

**Merged pull requests:**

- Head [\#26](https://github.com/nfarina/xmldoc/pull/26) ([wotzisname](https://github.com/wotzisname))
- Fix escaping of xml values and attributes [\#25](https://github.com/nfarina/xmldoc/pull/25) ([wotzisname](https://github.com/wotzisname))
- encode attribute values to keep xml valid [\#21](https://github.com/nfarina/xmldoc/pull/21) ([dmvjs](https://github.com/dmvjs))

## [v0.3.1](https://github.com/nfarina/xmldoc/tree/v0.3.1) (2015-05-22)

**Closed issues:**

- xmldoc error [\#19](https://github.com/nfarina/xmldoc/issues/19)
- Add Error Reporting [\#17](https://github.com/nfarina/xmldoc/issues/17)
- Need Line Number [\#14](https://github.com/nfarina/xmldoc/issues/14)
- How to retrieve the value in a few level down the nodes? [\#13](https://github.com/nfarina/xmldoc/issues/13)
- childNamed returns null not undefined [\#12](https://github.com/nfarina/xmldoc/issues/12)
- New version for npm? [\#10](https://github.com/nfarina/xmldoc/issues/10)
- getValueWithPath - xml namespace not supported? [\#9](https://github.com/nfarina/xmldoc/issues/9)
- High byte characters are not coming in correctly. [\#8](https://github.com/nfarina/xmldoc/issues/8)
- Add text as child nodes [\#7](https://github.com/nfarina/xmldoc/issues/7)
- descendantWithPath\(\) not always finds valid path [\#6](https://github.com/nfarina/xmldoc/issues/6)
- TypeError: Cannot call method 'apply' of undefined when parsing VMware vCloud Director XML [\#5](https://github.com/nfarina/xmldoc/issues/5)
- Serialization [\#2](https://github.com/nfarina/xmldoc/issues/2)
- can't create new XMLDocument… it seems to be undefined [\#1](https://github.com/nfarina/xmldoc/issues/1)

**Merged pull requests:**

- Escape ampersands and quotes as well [\#18](https://github.com/nfarina/xmldoc/pull/18) ([protobi](https://github.com/protobi))
- Added parse information to XmlElement [\#15](https://github.com/nfarina/xmldoc/pull/15) ([EToreo](https://github.com/EToreo))
- Add escaping '\<' and '\>' in toString\(\) [\#11](https://github.com/nfarina/xmldoc/pull/11) ([martnst](https://github.com/martnst))
- add whole document serialization via toString\(whole=true, compressed=true\) [\#4](https://github.com/nfarina/xmldoc/pull/4) ([jankuca](https://github.com/jankuca))

\* _This Change Log was automatically generated by [github_changelog_generator](https://github.com/skywinder/Github-Changelog-Generator)_
