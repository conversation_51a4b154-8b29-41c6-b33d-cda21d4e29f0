const PdfPrinter = require('pdfmake');
const fs = require('fs');
const path = require('path');

// Configuración de fuentes simplificada
const fonts = {
  Helvetica: {
    normal: 'Helvetica',
    bold: 'Helvetica-Bold',
  }
};

// Datos de ejemplo basados en la imagen
const planillaData = {
  codigo: '25897',
  version: '01.05',
  fecha: '2022-03-19 10:25:15',
  consecutivo: '0009',
  mensajero: '<PERSON>',
  registros: [
    {
      fechaRadicado: '25/02/25\n09:39:15',
      numeroRadicado: '20221129',
      asunto: 'Prueba',
      entidad: 'A.R.L Sura',
      remitente: 'A.R.L Sura',
      seccion: 'Gerencia',
      destinatario: '<PERSON>\nV<PERSON><PERSON>',
      folios: '2',
      cc: '',
      pasaA: '',
      firma: ''
    },
    {
      fechaRadicado: '25/02/25\n09:39:15',
      numeroRadicado: '20221129',
      asunto: 'Prueba',
      entidad: 'A.R.L <PERSON>',
      remitente: 'A.R.L Sura',
      seccion: 'Bienes y\nSuministros',
      destinatario: 'Catalina\nCastrillón Rúa',
      folios: '2',
      cc: 'X',
      pasaA: '',
      firma: ''
    },
    {
      fechaRadicado: '25/02/25\n09:39:15',
      numeroRadicado: '20221129',
      asunto: 'Prueba',
      entidad: 'A.R.L Sura',
      remitente: 'A.R.L Sura',
      seccion: 'Gestión\nDocumental',
      destinatario: 'Alexandra Yohana\nValencia Florez',
      folios: '2',
      cc: 'X',
      pasaA: '',
      firma: ''
    },
    {
      fechaRadicado: '25/02/25\n10:54:12',
      numeroRadicado: '20221130',
      asunto: 'Prueba 2',
      entidad: 'ADCAP\nCOLOMBIA\nS.A',
      remitente: 'ADCAP\nCOLOMBIA\nS.A',
      seccion: 'Subdirección\nCientífica',
      destinatario: 'Ana María\nGonzález Ramírez',
      folios: '2',
      cc: '',
      pasaA: '',
      firma: ''
    },
    {
      fechaRadicado: '03/07/25\n12:47:38',
      numeroRadicado: '20221131',
      asunto: 'prueba con copia',
      entidad: '+Verde S.A.S',
      remitente: '+Verde S.A.S',
      seccion: 'Gerencia',
      destinatario: 'Martha María\nVásquez Correa',
      folios: '1',
      cc: '',
      pasaA: '',
      firma: ''
    },
    {
      fechaRadicado: '03/07/25\n12:47:38',
      numeroRadicado: '20221131',
      asunto: 'prueba con copia',
      entidad: '+Verde S.A.S',
      remitente: '+Verde S.A.S',
      seccion: 'Dirección\nProyectos',
      destinatario: 'Cristian Marcelo\nMorales',
      folios: '1',
      cc: 'X',
      pasaA: '',
      firma: ''
    },
    {
      fechaRadicado: '03/07/25\n12:47:38',
      numeroRadicado: '20221131',
      asunto: 'prueba con copia',
      entidad: '+Verde S.A.S',
      remitente: '+Verde S.A.S',
      seccion: 'Gestión\nDocumental',
      destinatario: 'Alexandra Yohana\nValencia Florez',
      folios: '1',
      cc: 'X',
      pasaA: '',
      firma: ''
    }
  ]
};

// Función para generar el PDF
function generarPlanillaPDF() {
  const printer = new PdfPrinter(fonts);

  const docDefinition = {
    pageSize: 'A4',
    pageOrientation: 'landscape',
    pageMargins: [20, 20, 20, 20],
    defaultStyle: {
      font: 'Helvetica'
    },
    content: [
      // Encabezado
      {
        table: {
          widths: [100, '*', 150],
          body: [
            [
              { text: '[LOGO]', style: 'logo', border: [true, true, true, true] },
              { text: 'PLANILLA DE DISTRIBUCIÓN INTERNA 2022', style: 'title', border: [true, true, true, true] },
              {
                table: {
                  widths: ['*'],
                  body: [
                    [{ text: `Código: ${planillaData.codigo}`, style: 'headerInfo' }],
                    [{ text: `Versión: ${planillaData.version}`, style: 'headerInfo' }],
                    [{ text: `Fecha: ${planillaData.fecha}`, style: 'headerInfo' }]
                  ]
                },
                border: [true, true, true, true]
              }
            ]
          ]
        },
        layout: 'noBorders'
      },
      { text: '\n' },

      // Información del consecutivo y mensajero
      {
        table: {
          widths: [150, 300, '*'],
          body: [
            [
              { text: `Consecutivo: ${planillaData.consecutivo}`, style: 'info' },
              { text: `Mensajero: ${planillaData.mensajero}`, style: 'info' },
              { text: 'Firma: ________________________________', style: 'info' }
            ]
          ]
        },
        layout: {
          hLineWidth: () => 1,
          vLineWidth: () => 1
        }
      },
      { text: '\n' },

      // Tabla principal
      {
        table: {
          headerRows: 1,
          widths: [60, 60, 80, 60, 60, 60, 80, 30, 20, 40, 80],
          body: [
            // Encabezados
            [
              { text: 'FECHA DE\nRADICADO\nDD/MM/AA', style: 'tableHeader' },
              { text: 'NUMERO DE\nRADICADO', style: 'tableHeader' },
              { text: 'ASUNTO', style: 'tableHeader' },
              { text: 'ENTIDAD', style: 'tableHeader' },
              { text: 'REMITENTE', style: 'tableHeader' },
              { text: 'SECCION', style: 'tableHeader' },
              { text: 'DESTINATARIO', style: 'tableHeader' },
              { text: 'FOLIOS', style: 'tableHeader' },
              { text: 'CC', style: 'tableHeader' },
              { text: 'PASA A', style: 'tableHeader' },
              { text: 'FIRMA Y SELLO\nDE RECIBIDO', style: 'tableHeader' }
            ],
            // Datos
            ...planillaData.registros.map(registro => [
              { text: registro.fechaRadicado, style: 'tableCell' },
              { text: registro.numeroRadicado, style: 'tableCell' },
              { text: registro.asunto, style: 'tableCell' },
              { text: registro.entidad, style: 'tableCell' },
              { text: registro.remitente, style: 'tableCell' },
              { text: registro.seccion, style: 'tableCell' },
              { text: registro.destinatario, style: 'tableCell' },
              { text: registro.folios, style: 'tableCell', alignment: 'center' },
              { text: registro.cc, style: 'tableCell', alignment: 'center' },
              { text: registro.pasaA, style: 'tableCell' },
              { text: registro.firma, style: 'tableCell' }
            ])
          ]
        },
        layout: {
          hLineWidth: () => 1,
          vLineWidth: () => 1,
          hLineColor: () => '#000000',
          vLineColor: () => '#000000'
        }
      }
    ],
    styles: {
      logo: {
        fontSize: 12,
        alignment: 'center',
        margin: [5, 10]
      },
      title: {
        fontSize: 14,
        bold: true,
        alignment: 'center',
        margin: [5, 15]
      },
      headerInfo: {
        fontSize: 9,
        margin: [5, 2]
      },
      info: {
        fontSize: 10,
        margin: [5, 5]
      },
      tableHeader: {
        fontSize: 8,
        bold: true,
        alignment: 'center',
        margin: [2, 2],
        fillColor: '#f0f0f0'
      },
      tableCell: {
        fontSize: 7,
        margin: [2, 2]
      }
    }
  };

  const pdfDoc = printer.createPdfKitDocument(docDefinition);
  const outputPath = path.join(__dirname, 'output', 'planilla_distribucion.pdf');

  pdfDoc.pipe(fs.createWriteStream(outputPath));
  pdfDoc.end();

  console.log(`PDF generado exitosamente en: ${outputPath}`);
}

// Ejecutar la generación del PDF
generarPlanillaPDF();